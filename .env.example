APP_NAME="FRAMEWORK"
APP_ENV=local
APP_KEY=base64:yGZ3Co6Heh5kIjZmPLgRiQ9b/mNqqAEpuTErObD8Qmk=
APP_DEBUG=false
APP_URL=

LOG_CHANNEL=stack
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=
DB_PORT=3306
DB_DATABASE=
DB_USERNAME=
DB_PASSWORD=
DB_SOCKET=


BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DRIVER=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"



PREFIX_ADMIN="admin"
ADMIN_THEME="adminLte"
INSTALLATION=
IMPORT_OLD_DATABASE=false
DEMO_MODE="Off"

DB_CONNECTION_SECOND=
DB_PORT_SECOND=
DB_HOST_SECOND=
DB_DATABASE_SECOND=
DB_USERNAME_SECOND=
DB_PASSWORD_SECOND=

MAIL_DRIVER=sendmail
MAILGUN_DOMAIN=
MAILGUN_SECRET=
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=null
MAIL_FROM_NAME="${APP_NAME}"
