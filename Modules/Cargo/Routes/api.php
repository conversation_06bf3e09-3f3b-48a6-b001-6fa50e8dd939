<?php

use Illuminate\Http\Request;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:api')->get('/cargo', function (Request $request) {
    return $request->user();
});

// Auth Apis Routes
Route::prefix('v1/auth')->group(function () {

    Route::post('signup', 'Api\AuthController@signup');
    Route::post('login', 'Api\AuthController@login');

});

Route::get('get-wallet', 'Api\AuthController@getWallet');  // not in postman

// Shipments Apis Routes
Route::post('admin/shipments/create', array('uses' => 'ShipmentController@storeAPI'));
Route::post('admin/shipments/delete', array('uses' => 'ShipmentController@deleteAPI'));
Route::get('shipments', array('uses' => 'ShipmentController@getShipmentsAPI'));

Route::get('shipments-v2', array('uses' => 'ShipmentController@getShipmentsAPINew'));
Route::get('shipment_details', array('uses' => 'ShipmentController@getShipmentDetailsApi'));

Route::post('/contact_us', array('uses' => 'ShipmentController@contactus') );




Route::post('shipments-chat', array('uses' => 'ShipmentController@shipmentChatApi'));
Route::post('basic_import', array('uses' => 'ShipmentController@parseImportAPI'));
Route::post('smart_import', array('uses' => 'ShipmentController@smartImportAPI'));
Route::post('get_admin_token', array('uses' => 'ShipmentController@getAdminTokenAPI'));

Route::get('ConfirmationTypeMission', 'Api\ShipmentController@getConfirmationTypeMission');  // not in postman
Route::get('shipment-by-barcode', 'ShipmentController@ajaxGetShipmentByBarcode');
Route::get('shipmentPackages', 'Api\ShipmentController@getShipmentPackages');
Route::get('shipment-tracking', 'Api\ShipmentController@tracking');
Route::get('shipment-setting', 'Api\ShipmentController@getSetting');


Route::get('provider/wallet_history', 'Api\ShipmentController@providerWalletHistory');


// Missions Apis Routes
Route::post('createMission', 'ShipmentController@createMissionAPI'); // not in postman
Route::post('changeMissionStatus', 'Api\MissionsController@changeMissionApi');

Route::get('getMissionOfTheShipment', 'Api\MissionsController@getMissionOfTheShipment');


Route::post('remove-shipment-from-mission', 'Api\MissionsController@RemoveShipmetnFromMission'); // not in postman


Route::get('missions', 'Api\MissionsController@getCaptainMissions');
Route::get('missions-v2', 'Api\MissionsController@getCaptainMissionsNew');
Route::get('change_mission_priority', 'Api\MissionsController@changeMissionPriority');



//================================================================

// Get Reasons Api Route
Route::get('reasons', 'Api\MissionsController@getReasons');

Route::get('packages', 'Api\ShipmentController@ajaxGetPackages');
Route::get('DeliveryTimes', 'Api\ShipmentController@ajaxGetDeliveryTimes'); // not in postman
Route::get('MissionShipments', 'Api\ShipmentController@getMissionShipments');
Route::get('countries', 'Api\ShipmentController@countriesApi');
Route::get('states', 'Api\ShipmentController@ajaxGetStates');
Route::get('areas', 'Api\ShipmentController@ajaxGetAreas');
Route::get('notifications', 'Api\ShipmentController@ajaxGetNotifications');
Route::get('payment-types', 'Api\ShipmentController@getPaymentTypes');

// Address Apis Routes
Route::post('addAddress', 'ClientController@addNewAddressAPI');
Route::get('getAddresses', 'ClientController@getAddresses');



Route::post('create/flyer/order', 'ClientController@addFlyerOrderAPI');
Route::get('flyers', 'ClientController@getFlyersAPI');
Route::get('client/flyer/orders', 'ClientController@getClientFlyersOrdersAPI');

// Get Branches Api Route
Route::get('branchs', 'Api\ShipmentController@getBranchs');

// Show Register In Driver App Api Route
Route::get('show-register-in-driver-app', 'Api\ShipmentController@showRegisterInDriverApp'); // not in postman
