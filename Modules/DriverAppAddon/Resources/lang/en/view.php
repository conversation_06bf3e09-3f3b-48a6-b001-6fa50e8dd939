<?php

return [
    'app_name' => 'App Name',
    'driver_app_settings' => 'Driver App Settings',
    'application_icon' => 'Application Icon',
    'app_bundle_id' => 'App Bundle Id',
    'application_splash_screen_background' => 'Application splash screen background color (hex code, ex:#FFFFFF)',
    'application_splash_screen' => 'Application splash screen (The first page when app open)',
    'issuer_id' => 'Issuer ID',
    'key_id' => 'Key ID',
    'auth_key_file_content' => 'Add the AuthKey file content, this file for the IOS app',
    'apple_id' => 'Apple ID',
    'notifications_icon' => 'Enable or disable the small notifications icon for android',
    'small_notification_icon_color' => 'The color of the small notificatios icon for android (hex code, ex:#FFFFFF)',
    'small_notifications_icon' => 'Small Notifications Icon',
    'create_application' => 'Create Application',
    'cargo_purchase_code' => 'Cargo Purchase Code',
    'addon_purchase_code' => 'Driver App Addon Purchase Code',
    'google_service_ios' => 'Google Service Ios File',
    'google_service_android' => 'Google Service Android File',
    'application_in_progress_status' => 'Your application is in progress, estimated time from the sending request 7 days',
    'sending_request_date' => 'Your sending request date is',
    'application_created_successfully' => 'Your application has been created successfully',
    'app_name_shown' => "Your app's name shown on Play Store and App Store",
    'upload_application_icon' => 'Upload the application icon and it must be (1024*1024px), PNG and NOT transparent',
    'upload_application_splash' => 'Upload the application splash screen and it must be (512*512px), PNG and NOT transparent',
    'get_issuer_id' => 'You can get the Issuer ID, KEY ID, Password,
        of APP-SPECIFIC PASSWORDS and AuthKey file',
    'from_here' => 'From Here',
    'there_required' => 'and these are required if you need the IOS version',
    'ios_app_store_number' => "An IOS applications's store ID number can be found in the iTunes store URL
    as the string of numbers directly after id. For Example, in https://apps.apple.com/app/discy/id1535374585<br>
    the ID is: 1535374585",
    'upload_small_notification_icon' => 'The icon of the small notificatios icon for android and it must be (20*20px), PNG and NOT transparent',
    'upload_google_android_file' => 'The google-service.json file, this file for the Android app',
    'upload_google_ios_file' => 'The GoogleService-info.plist file, this file for the IOS app',
    'uploading' => 'Uploading',
    'sending' => 'Sending',
    'get_google_service' => 'You can visit the documentation for more information to get those files',
];
