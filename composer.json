{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "repositories": [{"type": "composer", "url": "https://satis.spatie.be"}], "require": {"php": "^7.3|^8.0", "fruitcake/laravel-cors": "^2.0", "genealabs/laravel-model-caching": "^0.11.3", "google/auth": "*", "guzzlehttp/guzzle": "^7.4", "harimayco/laravel-menu": "^1.4", "instamojo/instamojo-php": "^0.4.0", "intervention/image": "^2.6", "iyzico/iyzipay-php": "^2.0", "kreait/firebase-php": "^7.1", "laracasts/flash": "^3.0", "laravel/framework": "^8.54", "laravel/sanctum": "^2.11", "laravel/tinker": "^2.5", "league/omnipay": "^3.2", "livewire/livewire": "^2.0", "maatwebsite/excel": "^3.1", "mcamara/laravel-localization": "^1.7", "milon/barcode": "^8.0", "nesbot/carbon": "^2.67", "nexmo/client": "^2.4", "nwidart/laravel-modules": "8.2", "omnipay/paypal": "^3.0", "orangehill/iseed": "^3.0", "qirolab/laravel-themer": "^1.5", "razorpay/razorpay": "2.*", "spatie/laravel-activitylog": "^4.1", "spatie/laravel-medialibrary": "^9.0", "spatie/laravel-medialibrary-pro": "^1.4.1", "spatie/laravel-permission": "^5.1", "spatie/laravel-settings": "^2.3", "spatie/laravel-translatable": "^5.0", "sslwireless/sslwireless-sms": "^0.0.1", "stripe/stripe-php": "^7.0.0", "touhidurabir/laravel-stub-generator": "^1.0", "twilio/sdk": "^6.44", "unicodeveloper/laravel-paystack": "^1.0", "yajra/laravel-datatables-buttons": "^4.10", "yajra/laravel-datatables-oracle": "~9.0"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.6", "facade/ignition": "^2.5", "fakerphp/faker": "^1.9.1", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.2", "nunomaduro/collision": "^5.0", "phpunit/phpunit": "^9.3.3"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Modules\\": "Mo<PERSON>les/"}, "files": ["app/Helpers/functions/url_helpers.php", "app/Helpers/functions/helpers.php", "app/Helpers/functions/wedget_functions.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"composer/package-versions-deprecated": true, "php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true}