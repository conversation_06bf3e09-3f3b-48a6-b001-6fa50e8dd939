<?php
/**
 * Debug script to understand the columns validation issue
 */

// Configuration
$base_url = 'http://localhost/api'; // Change this to your actual API URL
$admin_email = '<EMAIL>';
$admin_password = '123456';

echo "=== DEBUGGING COLUMNS VALIDATION ISSUE ===\n\n";

// Step 1: Get admin token
echo "Step 1: Getting admin token...\n";

$token_data = [
    'email' => $admin_email,
    'password' => $admin_password
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $base_url . '/get_admin_token');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($token_data));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/x-www-form-urlencoded'
]);

$token_response = curl_exec($ch);
$token_result = json_decode($token_response, true);

if (!$token_result || !isset($token_result['success']) || !$token_result['success']) {
    echo "Failed to get token. Response: " . $token_response . "\n";
    exit;
}

$token = $token_result['token'];
echo "✓ Token obtained successfully\n\n";

// Step 2: Test debug endpoint to see what data is being received
echo "Step 2: Testing debug endpoint to see how data is received...\n";

// Your specific columns array
$columns = [
    0 => "type",
    1 => "shipping_date", 
    2 => "reciver_name",
    3 => "reciver_phone",
    4 => "reciver_address",
    5 => "to_area_id",
    6 => "to_state_id",
    7 => "amount_to_be_collected",
    8 => "open_shipment",
    9 => "order_id",
    10 => "comment"
];

// Create a temporary test file
$temp_file = tempnam(sys_get_temp_dir(), 'test_excel');
file_put_contents($temp_file, "test content"); // Just for testing

// Prepare multipart form data
$post_data = [];
foreach ($columns as $index => $column_name) {
    $post_data["columns[$index]"] = $column_name;
}
$post_data['shipments_file'] = new CURLFile($temp_file, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'test.xlsx');

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $base_url . '/debug_request');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'token: ' . $token
]);

$debug_response = curl_exec($ch);
$debug_result = json_decode($debug_response, true);

echo "Debug endpoint response:\n";
echo json_encode($debug_result, JSON_PRETTY_PRINT) . "\n\n";

// Step 3: Test basic_import with the same data
echo "Step 3: Testing basic_import endpoint...\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $base_url . '/basic_import');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'token: ' . $token
]);

$import_response = curl_exec($ch);
$import_result = json_decode($import_response, true);

echo "Basic import response:\n";
echo json_encode($import_result, JSON_PRETTY_PRINT) . "\n\n";

// Clean up
unlink($temp_file);
curl_close($ch);

echo "=== ANALYSIS ===\n";
if (isset($debug_result['debug_info']['columns'])) {
    $received_columns = $debug_result['debug_info']['columns'];
    echo "Columns received by server: " . json_encode($received_columns) . "\n";
    echo "Columns count: " . (is_array($received_columns) ? count($received_columns) : 'not array') . "\n";
    echo "Columns type: " . gettype($received_columns) . "\n";
} else {
    echo "No columns data received by server!\n";
}

echo "\n=== POSTMAN INSTRUCTIONS ===\n";
echo "1. GET TOKEN:\n";
echo "   POST: {$base_url}/get_admin_token\n";
echo "   Body: email={$admin_email}&password={$admin_password}\n\n";

echo "2. DEBUG REQUEST (test first):\n";
echo "   POST: {$base_url}/debug_request\n";
echo "   Headers: token: [your_token]\n";
echo "   Body (form-data):\n";
echo "     shipments_file: [any file]\n";
foreach ($columns as $index => $column_name) {
    echo "     columns[$index]: $column_name\n";
}

echo "\n3. BASIC IMPORT (after debug works):\n";
echo "   POST: {$base_url}/basic_import\n";
echo "   Headers: token: [your_token]\n";
echo "   Body (form-data): [same as debug request]\n";

echo "\nIf the debug endpoint shows columns correctly but basic_import fails,\n";
echo "then the issue is in the validation logic, not the data format.\n";
?>
