{"info": {"name": "Cargo Import API", "description": "Collection for testing the cargo import API endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "1. <PERSON>min <PERSON>", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "123456", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/get_admin_token", "host": ["{{base_url}}"], "path": ["api", "get_admin_token"]}}, "response": []}, {"name": "2. Basic Import", "request": {"method": "POST", "header": [{"key": "token", "value": "{{admin_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "shipments_file", "type": "file", "src": []}, {"key": "columns[0]", "value": "type", "type": "text"}, {"key": "columns[1]", "value": "branch_id", "type": "text"}, {"key": "columns[2]", "value": "shipping_date", "type": "text"}, {"key": "columns[3]", "value": "client_phone", "type": "text"}, {"key": "columns[4]", "value": "client_address", "type": "text"}, {"key": "columns[5]", "value": "reciver_name", "type": "text"}, {"key": "columns[6]", "value": "reciver_phone", "type": "text"}, {"key": "columns[7]", "value": "reciver_address", "type": "text"}, {"key": "columns[8]", "value": "from_country_id", "type": "text"}, {"key": "columns[9]", "value": "to_country_id", "type": "text"}, {"key": "columns[10]", "value": "from_state_id", "type": "text"}, {"key": "columns[11]", "value": "to_state_id", "type": "text"}, {"key": "columns[12]", "value": "from_area_id", "type": "text"}, {"key": "columns[13]", "value": "to_area_id", "type": "text"}, {"key": "columns[14]", "value": "payment_type", "type": "text"}, {"key": "columns[15]", "value": "payment_method_id", "type": "text"}, {"key": "columns[16]", "value": "attachments_before_shipping", "type": "text"}, {"key": "columns[17]", "value": "package_id", "type": "text"}, {"key": "columns[18]", "value": "description", "type": "text"}, {"key": "columns[19]", "value": "qty", "type": "text"}, {"key": "columns[20]", "value": "weight", "type": "text"}, {"key": "columns[21]", "value": "length", "type": "text"}, {"key": "columns[22]", "value": "width", "type": "text"}, {"key": "columns[23]", "value": "height", "type": "text"}, {"key": "columns[24]", "value": "amount_to_be_collected", "type": "text"}, {"key": "columns[25]", "value": "order_id", "type": "text"}, {"key": "columns[26]", "value": "delivery_time", "type": "text"}, {"key": "columns[27]", "value": "collection_time", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/basic_import", "host": ["{{base_url}}"], "path": ["api", "basic_import"]}}, "response": []}], "variable": [{"key": "base_url", "value": "http://localhost", "type": "string"}, {"key": "admin_token", "value": "", "type": "string"}]}