{"info": {"name": "Cargo Import API - Your Excel Format", "description": "Collection for testing with your specific Excel column order", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "1. <PERSON>min <PERSON>", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "123456", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/get_admin_token", "host": ["{{base_url}}"], "path": ["api", "get_admin_token"]}}, "response": []}, {"name": "2. Basic Import - Your Format", "request": {"method": "POST", "header": [{"key": "token", "value": "{{admin_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "shipments_file", "type": "file", "src": []}, {"key": "columns[0]", "value": "type", "type": "text"}, {"key": "columns[1]", "value": "shipping_date", "type": "text"}, {"key": "columns[2]", "value": "reciver_name", "type": "text"}, {"key": "columns[3]", "value": "reciver_phone", "type": "text"}, {"key": "columns[4]", "value": "reciver_address", "type": "text"}, {"key": "columns[5]", "value": "to_area_id", "type": "text"}, {"key": "columns[6]", "value": "to_state_id", "type": "text"}, {"key": "columns[7]", "value": "amount_to_be_collected", "type": "text"}, {"key": "columns[8]", "value": "open_shipment", "type": "text"}, {"key": "columns[9]", "value": "order_id", "type": "text"}, {"key": "columns[10]", "value": "comment", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/basic_import", "host": ["{{base_url}}"], "path": ["api", "basic_import"]}}, "response": []}], "variable": [{"key": "base_url", "value": "http://localhost", "type": "string"}, {"key": "admin_token", "value": "", "type": "string"}]}