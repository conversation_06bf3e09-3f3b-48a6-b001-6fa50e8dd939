<?php

return [

    'dashboard' => 'Dashboard',
    'website' => 'View Website',
    'setting' => 'Settings',
    'setting' => 'Settings',
    'theme_setting' => 'Theme Settings',
    'general_setting' => 'General Settings',
    'general_settings' => 'General Settings',
    'notifications_settings' => 'Notifications Settings',
    'google_settings' => 'Google Settings',
    'home' => 'Home',
    'pages' => 'Pages',
    'sign_out' => 'Sign Out',
    'filter' => 'Filter',
    'filter_options' => 'Filter options',
    'reset' => 'Reset',
    'apply' => 'Apply',
    'cancel' => 'Cancel',
    'from' => 'From',
    'to' => 'To',
    'or' => 'Or',
    'on' => 'on',
    'created_between' => 'Created between',
    'pick_date_range' => 'Pick date range',
    'loading' => 'Loading',

    'create_new' => 'Create new',

    'export' => 'Export',
    'search' => 'Search',
    'search_result_for' => 'Search result for',
    'search_by_phrase' => 'Search by phrase',
    'not_found_result' => 'Not found result',
    'reload' => 'Reload',
    'table_id' => 'ID',
    'edit_profile' => 'Edit profile',
    'view_profile' => 'View profile',
    'profile_details' => 'Profile details',
    'manage_access' => 'Manage access',
    'profile' => 'Profile',
    'edit' => 'Edit',
    'create' => 'Create',
    'discard' => 'Discard',
    'overview' => 'Overview',
    'approve' => 'Approve',
    'approve_selected' => 'Approve selected',
    'approval' => 'Approval',
    'approval_status' => 'Approval status',
    'approved' => 'Approved',
    'reject' => 'Reject',
    'reject_selected' => 'Reject selected',
    'rejected' => 'Rejected',
    'pending' => 'Pending',
    'save' => 'Save',
    'update' => 'Update',
    'delete' => 'Delete',
    'delete_selected' => 'Delete selected',
    'selected' => 'Selected',
    'manage' => 'Manage',
    'show' => 'Show',
    'hide' => 'Hide',
    'active' => 'Active',
    'deactive' => 'De-active',
    'activate' => 'Activate',
    'deactivate' => 'Deactivate',
    'activated' => 'Activated',
    'deactivated' => 'Deactivated',
    'activation' => 'Activation',
    'published' => 'Published',
    'publishing' => 'Publishing',
    'publish' => 'Publish',
    'publish_immediately' => 'Publish immediately',
    'save_draft' => 'Save draft',
    'draft' => 'Draft',
    'status' => 'Status',
    'rows' => 'Rows',
    'all' => 'All',
    'created_at' => 'Created at',
    'updated_at' => 'Updated at',
    'last_modified' => 'Last modified',
    'action' => 'Action',
    'select_all' => 'Select all',
    'choose' => 'Choose',

    'change_avatar' => 'Change avatar',
    'cancel_avatar' => 'Cancel avatar',
    'remove_avatar' => 'Remove avatar',

    'change_image' => 'Change image',
    'cancel_image' => 'Cancel image',
    'remove_image' => 'Remove image',
    'hint_image_ext' => 'Allowed file types: png, jpg, jpeg.',

    'msg_error_data' => 'The given data was invalid.',

    'translate' => 'Translate',
    'example' => 'Example',

    'modal_message_delete' => 'Are you sure delete ',
    'modal_message_approve' => 'Are you sure approve ',
    'modal_message_reject' => 'Are you sure reject ',


    'stay_connected' => 'Stay Connected',
    'social_counters' => 'Social Counters',
    'add_new_platform' => 'Add new platform',
    'remove_platform' => 'Remove platform',
    'social_counters_data' => [
        'platform' => 'Platform',
        'count' => 'Count',
        'count_types' => [
            'fans' => 'Fans',
            'followers' => 'Followers',
            'subscribers' => 'Subscribers',
        ]
    ],


    'breaking_news' => 'Breaking News',
    'editors_pick' => 'Editor’s Pick',
    'most_popular' => 'Most Popular',
    'latest_articles' => 'Latest Articles',
    'trending_reviews' => 'Trending Reviews',

    'read_more' => 'Read More &raquo;',

    // THEME SETTINGS
    'setting' => 'Setting',
    'module_setting' => ':module setting',
    'setting_modules' => 'Setting modules',
    'module_name' => 'Module name',
    'item' => 'Item',
    'add_new_item' => 'Add new item',
    'manage_setting_module' => 'Manage setting module',
    'ungroup' => 'Ungroup',
    'theme_setting' => 'Theme setting',
    'no_theme_setting' => 'Not found theme setting',
    'add_section' => 'Add Section',
    'add_widget' => 'Add Widget',
    'widget' => 'Widget',
    'add_container' => 'Add Container',
    'container' => 'Container',
    'container_setting' => 'Container Setting',
    'container_sections' => 'Container Sections',
    'container_width' => 'Container Width',
    'display_container' => 'Display Container',
    'container_sticky' => 'Container Sticky',
    'container_bg_img' => 'Container Background Image',
    'remove_container' => 'Remove Container',
    'no_sections' => 'No Sections',
    'msg_theme_setting_updated' => 'Theme setting has been updated successfully.',


    'remove_section' => 'Remove section',
    'duplicate' => 'Duplicate',
    'move_up' => 'Move Up',
    'move_down' => 'Move Down',
    'to_top' => 'To Top',
    'to_bottom' => 'To Bottom',
    'notifications' => 'Notifications',
    'no_new_notifications' => 'No new notifications',
    'see_all_notifications' => 'See All Notifications',
    'demo_mode' => 'This action is disabled in demo mode',
    'saved' => 'Saved successfully.',

    'LOG_IN_TO_YOUR_ACCOUNT' => 'LOG IN TO YOUR ACCOUNT',
    'Password' => 'Password',
    'Email' => 'Email',
    'remember_me' => 'Remember me',
    'login' => 'Login',
    'forgot_password' => 'Forgot Password ?',
    'register_as_a_customer' => 'Register as a customer',
    'demo_login_details' => 'Demo Login Details',
    'demo_details' => "If any user from below didn't work for any reason, it may be a visitor has changed it's data, the data will be reset again every 12 hours",
    'ADMIN' => 'ADMIN',
    'EMPLOYEE' => 'EMPLOYEE',
    'BRANCH_MANAGER' => 'BRANCH MANAGER',
    'DRIVER_CAPTAIN' => 'DRIVER/CAPTAIN',
    'CUSTOMER' => 'CUSTOMER',
    'click_to_copy' => 'Click to Copy',
    'sign_in' => 'Sign In',

    'reports' => 'Reports',
    'system_update' => 'System Update',
    'support' => 'Support',
    'ZIP_file_to_import' => 'ZIP File To Import',
    'yes' => 'Yes',
    'no' => 'No',
    'are_you_sure' => 'Are You Sure?',
    'updated_successfully_to_version' => 'Updated Successfully To Version',
    'please_update_version' => 'Please Update Version',
    'disable' => 'Disable',
    'system_update_note' => "If you want to take advantage of the latest update
        And you are in an old version, you have to download all the versions in order until you reach the update you want

        Example :

        If you are using version 6.0 and you want gold to 6.3, you have to take into account the versions between them

        You are now on version 6.0

        You'll start upgrading to 6.1, then 6.2, and so on until you reach the version you want.",
    'backup_necessary' => 'It is necessary to take a backup copy of the system files and databases before make the update',
    'this_help_you' => 'See This To Help',
    'themes' => 'Themes',
    'choose_theme' => 'Choose Theme',
    'active_now' => 'Active Now',
    'active_successfully' => 'Active Successfully',

    'main_theme' => 'Main Theme',
    'easyship_theme' => 'Default Theme',
    'flextock_theme' => 'Flextock Theme',
    'qwintry_theme' => 'Qwintry Theme',
    'goshippo_theme' => 'Goshippo Theme',
    'shipito_theme' => 'Shipito Theme',
    'timeglobalshipping_theme' => 'Time Global Theme',
    // upload addon
    'upload_addon' => 'Upload Addon',
    'addon_uploaded_successfully' => 'Addon has been uploaded successfully',
    'upload_addon_image' => 'Upload Addon Image',
    'this_addon_is_already_exist' => 'This Addon is already exist',
    'addons' => 'Addons',
    'There_no_addons_here' => 'There’s no addons here, you can purchase new addons',
    'from_here' => 'From Here',
    'send_app'=>'Send App',
    // app requests
    'app_requests' => 'App Requests',
    'purchase_code' => 'Purchase Code',
    'envato_status' => 'Envato Status',
    'app_status' => 'App Status',
    'actions' => 'Actions',
    'customer_website' => 'Customer Website',
    'new' => 'New',
    'in_progress' => 'In Progress',
    'done' => 'Done',
    'status_update_successfully' => 'Status Updated Successfully',
    'email_successfully'=>'The data has been sent to the email successfully',
    'app_name' => 'App Name',
    'android_url' => 'Android URL',
    'ios_url' => 'IOS URL',
    'app_request_updated_successfully' => 'App Request Updated Successfully',
    'upload' => 'Upload',
    'uploading' => 'Uploading',
    'add_new_addons' => 'Add New Addons',
    'configure_now' => 'Configure Now',
    'addon_deleted_successfully' => 'Addon deleted successfully',
    'disable' => 'Disable',
    'you_just_change_status' => 'Status has been update successfully',
    'backup_database' => 'Backup Database',
    'backup_database_note' => 'If you wish to create a backup of your database, you can do so by clicking on "create backup" button in the database user interface. This process is usually performed periodically to prevent any loss of data in case of system failure. Therefore, it is highly recommended to perform this process regularly and keep the backups on another device or external storage media to protect the data and ensure its recovery in case of any malfunction.',
    'backup_database_button' => 'Create Backup',
];

