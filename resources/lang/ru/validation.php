<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines contain the default error messages used by
    | the validator class. Some of these rules have multiple versions such
    | as the size rules. Feel free to tweak each of these messages here.
    |
    */

    'accepted' =>'Атрибут : должен быть принят.',
    'accepted_if' => 'Атрибут : должен быть принят, если :other равно :value.',
    'active_url' => 'Атрибут :не является допустимым URL.',
    'after' => 'Атрибут : должен быть датой после :date.',
    'after_or_equal' => 'Атрибут : должен быть датой после :date или равным :date.',
    'alpha' => 'Атрибут : должен содержать только буквы.',
    'alpha_dash' => 'Атрибут : должен содержать только буквы, цифры, дефисы и символы подчеркивания.',
    'alpha_num' => 'Атрибут : должен содержать только буквы и цифры.',
    'array' => 'Атрибут : должен быть массивом.',
    'before' => 'Атрибут : должен быть датой до :date.',
    'before_or_equal' => 'Атрибут : должен быть датой, предшествующей :date или равной ей.',
    'between' => [
        'numeric' => 'Атрибут : должен быть между :min и :max.',
        'file' => 'Атрибут : должен быть между :min и :max килобайтами.',
        'string' => 'Атрибут : должен находиться между символами :min и :max.',
        'array' => 'Атрибут : должен иметь от :min до :max элементов.',
    ],
    'boolean' => 'Поле :attribute должно быть истинным или ложным.',
    'confirmed' => 'Подтверждение :attribute не совпадает.',
    'current_password' => 'Пароль неверен.',
    'date' => 'Атрибут :не является действительной датой.',
    'date_equals' => 'Атрибут : должен быть датой, равной :date.',
    'date_format' => 'Атрибут :не соответствует формату :format.',
    'different' => ':attribute и :other должны быть разными.',
    'digits' => 'Атрибут : должен быть :digits digits.',
    'digits_between' => 'Атрибут : должен быть между цифрами :min и :max.',
    'dimensions' => 'Атрибут : имеет недопустимые размеры изображения.',
    'distinct' => 'Поле :attribute имеет повторяющееся значение.',
    'email' => 'Атрибут : должен быть действительным адресом электронной почты.',
    'ends_with' => 'Атрибут : должен заканчиваться одним из следующих: :values.',
    'exists' => 'Выбранный :атрибут недействителен.',
    'file' => 'Атрибут : должен быть файлом.',
    'filled' => 'Поле :attribute должно иметь значение.',
    'gt' => [
        'numeric' => 'Атрибут : должен быть больше :value.',
        'file' => 'Атрибут : должен быть больше :value килобайт.',
        'string' => 'Атрибут : должен быть больше символов :value.',
        'array' => 'Атрибут : должен содержать больше элементов, чем :value.',
    ],
    'gte' => [
        'numeric' => 'Атрибут : должен быть больше или равен :value.',
        'file' => 'Атрибут : должен быть больше или равен :value килобайтам.',
        'string' => 'Атрибут : должен быть больше или равен :value символов.',
        'array' => 'Атрибут : должен содержать элементы :value или более.',
    ],
    'image' => 'Атрибут : должен быть изображением.',
    'in' => 'Выбранный :атрибут недействителен.',
    'in_array' => 'Поле :attribute не существует в :other.',
    'integer' => 'Атрибут : должен быть целым числом.',
    'ip' => 'Атрибут : должен быть действительным IP-адресом.',
    'ipv4' => 'Атрибут : должен быть действительным адресом IPv4.',
    'ipv6' => 'Атрибут : должен быть действительным адресом IPv6.',
    'json' => 'Атрибут : должен быть допустимой строкой JSON.',
    'lt' => [
        'numeric' =>'Атрибут : должен быть меньше :value.',
        'file' => 'Атрибут : должен быть меньше :value килобайт.',
        'string' => 'Атрибут : должен быть меньше символов :value.',
        'array' => 'Атрибут : должен содержать меньше элементов :value.',
    ],
    'lte' => [
        'numeric' => 'Атрибут : должен быть меньше или равен :value.',
        'file' => 'Атрибут : должен быть меньше или равен :value килобайтам.',
        'string' => 'Атрибут : должен быть меньше или равен :value символов.',
        'array' => 'Атрибут : не может содержать более :value элементов.',
    ],
    'max' => [
        'numeric' => 'Атрибут : не должен быть больше :max.',
        'file' => 'Атрибут : не должен превышать :max килобайт.',
        'string' => 'Атрибут : не должен превышать :max символов.',
        'array' => 'Атрибут : не должен содержать более :max элементов.',
    ],
    'mimes' => 'Атрибут : должен быть файлом типа: :values.',
    'mimetypes' => 'Атрибут : должен быть файлом типа: :values.',
    'min' => [
        'numeric' => 'Атрибут : должен быть не меньше :min.',
        'file' => 'Атрибут : должен быть не менее :min килобайт.',
        'string' => 'Атрибут : должен содержать не менее :min символов.',
        'array' => 'Атрибут : должен содержать не менее :min элементов.',
    ],
    'multiple_of' => 'Атрибут : должен быть кратен :value.',
    'not_in' => 'Выбранный :атрибут недействителен.',
    'not_regex' => 'Недопустимый формат :attribute.',
    'numeric' => 'Атрибут : должен быть числом.',
    'password' => 'Пароль неверен.',
    'present' => 'Поле :attribute должно присутствовать.',
    'regex' => 'Недопустимый формат :attribute.',
    'required' => 'Поле :attribute обязательно.',
    'required_if' => 'Поле :attribute обязательно, если :other равно :value.',
    'required_unless' => 'Поле :attribute является обязательным, если :other не находится в :values.',
    'required_with' => 'Поле :attribute обязательно, если присутствует :values.',
    'required_with_all' => 'Поле :attribute обязательно, если присутствуют :values.',
    'required_without' => 'Поле :attribute является обязательным, если :values ​​отсутствует.',
    'required_without_all' => 'Поле :attribute является обязательным, если ни одно из :value не присутствует.',
    'prohibited' => 'Поле :attribute запрещено.',
    'prohibited_if' => 'Поле :attribute запрещено, если :other равно :value.',
    'prohibited_unless' => 'Поле :attribute запрещено, если :other не находится в :values.',
    'prohibits' =>'Поле :attribute запрещает присутствие :other.',
    'same' => ':attribute и :other должны совпадать.',
    'size' => [
        'numeric' => 'Атрибут : должен быть :size.',
        'file' => 'Атрибут : должен быть :size килобайт.',
        'string' => 'Атрибут : должен быть размером :size символов.',
        'array' => 'Атрибут : должен содержать элементы :size.',
    ],
    'starts_with' => 'Атрибут : должен начинаться с одного из следующих: :values.',
    'string' => 'Атрибут : должен быть строкой.',
    'timezone' => 'Атрибут : должен быть действительным часовым поясом.',
    'unique' => 'Атрибут : уже занят.',
    'uploaded' => 'Не удалось загрузить атрибут :attribute.',
    'url' => 'Атрибут : должен быть действительным URL.',
    'uuid' => 'Атрибут : должен быть действительным UUID.',

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | Here you may specify custom validation messages for attributes using the
    | convention "attribute.rule" to name the lines. This makes it quick to
    | specify a specific custom language line for a given attribute rule.
    |
    */

    'custom' => [
        'attribute-name' => [
            'rule-name' => 'пользовательское сообщение',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Attributes
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to swap our attribute placeholder
    | with something more reader friendly such as "E-Mail Address" instead
    | of "email". This simply helps us make our message more expressive.
    |
    */

    'attributes' => [],

];