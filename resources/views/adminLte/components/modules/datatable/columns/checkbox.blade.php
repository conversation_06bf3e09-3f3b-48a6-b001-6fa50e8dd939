@if ((isset($ifHide) && !$ifHide) || !isset($ifHide))




    @if (isset($type) && $type == 'mission' && ($model->type == __('cargo::view.delivery')  && ($model->status_id = 5)))
        @php
            $ShipmentMission = Modules\Cargo\Entities\ShipmentMission::where('mission_id', $model->id)->first();
            $shipment = Modules\Cargo\Entities\Shipment::find($ShipmentMission->shipment_id);
           // dd($model->type  , $model->status_id  ) ;
        @endphp

        <div class="form-check form-check-sm form-check-custom form-check-solid">
            <input class="form-check-input checkbox-row" type="checkbox" name="selected-rows[]"
                data-row-id="{{ $model->id }}"
                data-amount="{{  ($model->status_id = 5) ? $shipment->amount_to_be_collected : '0' }}">
        </div>
    @else
        <div class="form-check form-check-sm form-check-custom form-check-solid">
            <input class="form-check-input checkbox-row" type="checkbox" name="selected-rows[]"
                data-row-id="{{ $model->id }}">
        </div>
    @endif



@endif
