<!DOCTYPE html>
<html>

<head>
	<meta charset="utf-8">
	<title>Installation Wizard</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<link rel="stylesheet" href="{{ asset('assets/installation/fonts/material-design-iconic-font/css/material-design-iconic-font.css') }}">
	<link rel="stylesheet" href="{{ asset('assets/installation/css/style.css') }}">

	@yield('style')

	<script>
		var AIZ = AIZ || {};
	</script>
<body>
	<div class="wrapper">
		<div class="image-holder"> <img src="{{ asset('assets/installation/images/form-wizard.png') }}" alt=""> </div>


        @yield('content')

	</div>

    <!--begin::Global Config(global config for global JS scripts)-->
    <script>
        var KTAppSettings = {
            "breakpoints": {
                "sm": 576,
                "md": 768,
                "lg": 992,
                "xl": 1200,
                "xxl": 1400
            },
            "colors": {
                "theme": {
                    "base": {
                        "white": "#ffffff",
                        "primary": "#3699FF",
                        "secondary": "#E5EAEE",
                        "success": "#1BC5BD",
                        "info": "#8950FC",
                        "warning": "#FFA800",
                        "danger": "#F64E60",
                        "light": "#E4E6EF",
                        "dark": "#181C32"
                    },
                    "light": {
                        "white": "#ffffff",
                        "primary": "#E1F0FF",
                        "secondary": "#EBEDF3",
                        "success": "#C9F7F5",
                        "info": "#EEE5FF",
                        "warning": "#FFF4DE",
                        "danger": "#FFE2E5",
                        "light": "#F3F6F9",
                        "dark": "#D6D6E0"
                    },
                    "inverse": {
                        "white": "#ffffff",
                        "primary": "#ffffff",
                        "secondary": "#3F4254",
                        "success": "#ffffff",
                        "info": "#ffffff",
                        "warning": "#ffffff",
                        "danger": "#ffffff",
                        "light": "#464E5F",
                        "dark": "#ffffff"
                    }
                },
                "gray": {
                    "gray-100": "#F3F6F9",
                    "gray-200": "#EBEDF3",
                    "gray-300": "#E4E6EF",
                    "gray-400": "#D1D3E0",
                    "gray-500": "#B5B5C3",
                    "gray-600": "#7E8299",
                    "gray-700": "#5E6278",
                    "gray-800": "#3F4254",
                    "gray-900": "#181C32"
                }
            },
            "font-family": "Helvetica"
        };
    </script>
    <!--end::Global Config-->

    <!--begin::Global Theme Bundle(used by all pages)-->
    <script src="{{ asset('assets/lte/plugins/global/plugins.bundle.js') }}"></script>
    {{-- <script src="{{ asset('assets/dashboard/plugins/custom/prismjs/prismjs.bundle.js') }}"></script> --}}
    <script src="{{ asset('assets/lte/js/scripts.bundle.js') }}"></script>
    <!--end::Global Theme Bundle-->

	{{-- <script src="{{ asset('assets/js/vendors.js') }}" ></script> --}}
	{{-- <script src="{{ asset('assets/js/aiz-core.js') }}" ></script> --}}


    @yield('script')

    <script type="text/javascript">
        @foreach (session('flash_notification', collect())->toArray() as $message)
            AIZ.plugins.notify('{{ $message['level'] }}', '{{ $message['message'] }}');
        @endforeach
    </script>
</body>

</html>
