<?php
/**
 * Test script for the basic_import API endpoint
 * This script helps test the API with proper authentication and column format
 */

// Configuration
$base_url = 'http://localhost/api'; // Change this to your actual API URL
$admin_email = '<EMAIL>';
$admin_password = '123456'; // Change this to your actual admin password

// Step 1: Get admin token
echo "Step 1: Getting admin token...\n";

$token_data = [
    'email' => $admin_email,
    'password' => $admin_password
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $base_url . '/get_admin_token');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($token_data));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/x-www-form-urlencoded'
]);

$token_response = curl_exec($ch);
$token_result = json_decode($token_response, true);

if (!$token_result['success']) {
    echo "Failed to get token: " . $token_result['message'] . "\n";
    exit;
}

$token = $token_result['token'];
echo "Token obtained: " . substr($token, 0, 20) . "...\n";

// Step 2: Test basic_import with proper column format
echo "\nStep 2: Testing basic_import API...\n";

// Create sample Excel file path (you need to provide this)
$excel_file_path = '/path/to/your/sample.xlsx'; // Change this to your actual file path

if (!file_exists($excel_file_path)) {
    echo "Excel file not found at: $excel_file_path\n";
    echo "Please create a sample Excel file or update the path.\n";
    exit;
}

// Prepare columns array - this is the correct format
$columns = [
    0 => "type",
    1 => "branch_id", 
    2 => "shipping_date",
    3 => "client_phone",
    4 => "client_address",
    5 => "reciver_name",
    6 => "reciver_phone", 
    7 => "reciver_address",
    8 => "from_country_id",
    9 => "to_country_id",
    10 => "from_state_id",
    11 => "to_state_id",
    12 => "from_area_id", 
    13 => "to_area_id",
    14 => "payment_type",
    15 => "payment_method_id",
    16 => "attachments_before_shipping",
    17 => "package_id",
    18 => "description",
    19 => "qty",
    20 => "weight",
    21 => "length",
    22 => "width", 
    23 => "height",
    24 => "amount_to_be_collected",
    25 => "order_id",
    26 => "delivery_time",
    27 => "collection_time"
];

// Prepare multipart form data
$post_data = [];
foreach ($columns as $index => $column_name) {
    $post_data["columns[$index]"] = $column_name;
}
$post_data['shipments_file'] = new CURLFile($excel_file_path);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $base_url . '/basic_import');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'token: ' . $token  // This is the correct header format
]);

$import_response = curl_exec($ch);
$import_result = json_decode($import_response, true);

echo "Import API Response:\n";
echo json_encode($import_result, JSON_PRETTY_PRINT) . "\n";

curl_close($ch);

// Display instructions for Postman
echo "\n" . str_repeat("=", 60) . "\n";
echo "POSTMAN TESTING INSTRUCTIONS:\n";
echo str_repeat("=", 60) . "\n";
echo "1. First, get your token:\n";
echo "   POST: {$base_url}/get_admin_token\n";
echo "   Body (form-data):\n";
echo "     email: {$admin_email}\n";
echo "     password: {$admin_password}\n\n";

echo "2. Then use the basic_import endpoint:\n";
echo "   POST: {$base_url}/basic_import\n";
echo "   Headers:\n";
echo "     token: [YOUR_TOKEN_FROM_STEP_1]\n\n";
echo "   Body (form-data):\n";
echo "     shipments_file: [YOUR_EXCEL_FILE]\n";
foreach ($columns as $index => $column_name) {
    echo "     columns[$index]: $column_name\n";
}

echo "\nNote: Make sure your Excel file has the data in the same order as the columns array!\n";
?>
