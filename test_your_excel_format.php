<?php
/**
 * Test script for your specific Excel format
 * Columns: type, shipping_date, reciver_name, reciver_phone, reciver_address, to_area_id, to_state_id, amount_to_be_collected, open_shipment, order_id, comment
 */

// Configuration
$base_url = 'http://localhost/api'; // Change this to your actual API URL
$admin_email = '<EMAIL>';
$admin_password = '123456'; // Change this to your actual admin password

echo "Testing with your Excel column format...\n";
echo "Columns: type, shipping_date, reciver_name, reciver_phone, reciver_address, to_area_id, to_state_id, amount_to_be_collected, open_shipment, order_id, comment\n\n";

// Step 1: Get admin token
echo "Step 1: Getting admin token...\n";

$token_data = [
    'email' => $admin_email,
    'password' => $admin_password
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $base_url . '/get_admin_token');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($token_data));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/x-www-form-urlencoded'
]);

$token_response = curl_exec($ch);
$token_result = json_decode($token_response, true);

if (!$token_result || !isset($token_result['success']) || !$token_result['success']) {
    echo "Failed to get token. Response: " . $token_response . "\n";
    exit;
}

$token = $token_result['token'];
echo "Token obtained: " . substr($token, 0, 20) . "...\n";

// Your specific columns array
$columns = [
    0 => "type",
    1 => "shipping_date", 
    2 => "reciver_name",
    3 => "reciver_phone",
    4 => "reciver_address",
    5 => "to_area_id",
    6 => "to_state_id",
    7 => "amount_to_be_collected",
    8 => "open_shipment",
    9 => "order_id",
    10 => "comment"
];

echo "\nColumns array for your Excel format:\n";
foreach ($columns as $index => $column_name) {
    echo "  columns[$index]: $column_name\n";
}

echo "\n" . str_repeat("=", 80) . "\n";
echo "POSTMAN INSTRUCTIONS FOR YOUR EXCEL FORMAT:\n";
echo str_repeat("=", 80) . "\n";

echo "\n1. GET ADMIN TOKEN:\n";
echo "   POST: {$base_url}/get_admin_token\n";
echo "   Body (form-data):\n";
echo "     email: {$admin_email}\n";
echo "     password: {$admin_password}\n\n";

echo "2. BASIC IMPORT WITH YOUR COLUMNS:\n";
echo "   POST: {$base_url}/basic_import\n";
echo "   Headers:\n";
echo "     token: [YOUR_TOKEN_FROM_STEP_1]\n\n";
echo "   Body (form-data):\n";
echo "     shipments_file: [YOUR_EXCEL_FILE]\n";
foreach ($columns as $index => $column_name) {
    echo "     columns[$index]: $column_name\n";
}

echo "\n" . str_repeat("=", 80) . "\n";
echo "SAMPLE EXCEL DATA FORMAT:\n";
echo str_repeat("=", 80) . "\n";
echo "Row 1 (Header): type | shipping_date | reciver_name | reciver_phone | reciver_address | to_area_id | to_state_id | amount_to_be_collected | open_shipment | order_id | comment\n";
echo "Row 2 (Data):   1    | 2024-01-15    | John Doe     | 01012345678   | 123 Main St     | 1          | 1           | 100                    | no            | ORD001   | Test comment\n";

echo "\nNOTES:\n";
echo "- type: 1 = delivery, 3 = pickup\n";
echo "- reciver_phone: Must be Egyptian format (01xxxxxxxxx)\n";
echo "- to_area_id: Must be valid area ID from database\n";
echo "- to_state_id: Must be valid state ID from database\n";
echo "- open_shipment: 'yes' or 'no'\n";
echo "- amount_to_be_collected: Numeric value\n";

echo "\n" . str_repeat("=", 80) . "\n";
echo "POSTMAN COLLECTION JSON (Import this into Postman):\n";
echo str_repeat("=", 80) . "\n";

$postman_collection = [
    "info" => [
        "name" => "Cargo Import API - Your Format",
        "description" => "Collection for testing with your specific Excel format",
        "schema" => "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
    ],
    "item" => [
        [
            "name" => "1. Get Admin Token",
            "request" => [
                "method" => "POST",
                "header" => [],
                "body" => [
                    "mode" => "formdata",
                    "formdata" => [
                        ["key" => "email", "value" => $admin_email, "type" => "text"],
                        ["key" => "password", "value" => $admin_password, "type" => "text"]
                    ]
                ],
                "url" => [
                    "raw" => "{{base_url}}/api/get_admin_token",
                    "host" => ["{{base_url}}"],
                    "path" => ["api", "get_admin_token"]
                ]
            ]
        ],
        [
            "name" => "2. Basic Import - Your Format",
            "request" => [
                "method" => "POST",
                "header" => [
                    ["key" => "token", "value" => "{{admin_token}}", "type" => "text"]
                ],
                "body" => [
                    "mode" => "formdata",
                    "formdata" => array_merge(
                        [["key" => "shipments_file", "type" => "file", "src" => []]],
                        array_map(function($index, $column) {
                            return ["key" => "columns[$index]", "value" => $column, "type" => "text"];
                        }, array_keys($columns), $columns)
                    )
                ],
                "url" => [
                    "raw" => "{{base_url}}/api/basic_import",
                    "host" => ["{{base_url}}"],
                    "path" => ["api", "basic_import"]
                ]
            ]
        ]
    ],
    "variable" => [
        ["key" => "base_url", "value" => "http://localhost", "type" => "string"],
        ["key" => "admin_token", "value" => "", "type" => "string"]
    ]
];

echo json_encode($postman_collection, JSON_PRETTY_PRINT);
?>
